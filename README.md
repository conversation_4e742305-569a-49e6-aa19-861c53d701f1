# cv_chat_mcp

Parse your resume so the server can answer questions like “What role did I have at my last position?

# 📘 Project Title

A full-stack Node.js application built with TypeScript, Express, and EJS for server-side rendering.

## 🚀 Live Demo

- 🔗 [Live Render App](https://your-render-link.com)
- 🔗 [GitHub Repository](https://github.com/your-username/your-repo)

## 📌 Table of Contents

- [About the Project](#about-the-project)
- [Tech Stack](#tech-stack)
- [Features](#features)
- [Getting Started](#getting-started)
- [Folder Structure](#folder-structure)
- [Scripts](#scripts)
- [Environment Variables](#environment-variables)
- [Deployment](#deployment)
- [License](#license)

---

## 🧾 About the Project

This project is a backend-focused web application that uses:

- **Express.js** for routing and server logic
- **EJS** as a lightweight templating engine
- **TypeScript** for scalable and safer code
- **Nodemailer** for sending emails (e.g., contact form or verification)
- **dotenv** to securely manage environment variables

It is structured with maintainability and extensibility in mind using a modular MVC pattern.

---

## 🛠 Tech Stack

- **Node.js**
- **Express.js**
- **TypeScript**
- **EJS**
- **Nodemailer**
- **dotenv**
- **Render** (Deployment)
- **Docker** _(optional, if included)_

---

## 🌟 Features

- 📄 Server-side rendering with EJS
- 🛠 Typed backend logic with TypeScript
- 📬 Email sending via Nodemailer
- 🔐 Secure environment variable management
- 📁 Clean folder structure (controllers, routes, views)
- 🚀 Deployed on Render

---

## 🧰 Getting Started

### Prerequisites

- Node.js >= 16
- npm or yarn
- Git

### Installation

```bash
git clone https://github.com/your-username/your-repo.git
cd your-repo
npm install
```
