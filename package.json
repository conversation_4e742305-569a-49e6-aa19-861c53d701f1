{"name": "cv_chat_mcp", "version": "1.0.0", "description": "Parse your resume so the server can answer questions like “What role did I have at my last position?", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon --watch src --ext ts --exec \"ts-node\" src/server.ts", "build": "tsc", "start": "node dist/server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.11.0", "dotenv": "^17.2.1", "ejs": "^3.1.10", "express": "^5.1.0", "mailtrap": "^4.2.0", "mammoth": "^1.10.0", "multer": "^2.0.2", "nodemailer": "^6.10.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/express": "^5.0.3", "@types/multer": "^2.0.0", "@types/node": "^24.1.0", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}